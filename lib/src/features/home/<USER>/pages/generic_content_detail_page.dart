import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/content_item_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_podcast_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/widgets/enhanced_podcast_player_widget.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:flutter_html/flutter_html.dart'; // Import flutter_html

class GenericContentDetailPage extends StatefulWidget {
  final ContentItemModel contentItem;

  const GenericContentDetailPage({super.key, required this.contentItem});

  static const String routeName = '/content-detail';

  @override
  State<GenericContentDetailPage> createState() =>
      _GenericContentDetailPageState();
}

class _GenericContentDetailPageState extends State<GenericContentDetailPage> {
  final PocketBaseService _pbService = PocketBaseService();

  @override
  void initState() {
    super.initState();
  }

  /// Get podcast audio URL from media_file
  String? _getPodcastAudioUrl() {
    if (widget.contentItem.mediaFile == null ||
        widget.contentItem.mediaFile!.isEmpty) {
      return null;
    }

    try {
      return Uri.parse(
        '${_pbService.client.baseURL}/api/files/${widget.contentItem.collectionId}/${widget.contentItem.id}/${widget.contentItem.mediaFile}',
      ).toString();
    } catch (e) {
      LoggerService.error('Error constructing podcast audio URL', e);
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final shadTheme = ShadTheme.of(context);
    final dateFormat = DateFormat.yMMMd();
    final pb = PocketBaseService().client;
    final imageUrl = widget.contentItem.getImageUrl(pb);
    final bool hasImage = imageUrl != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.contentItem.title,
          style: shadTheme.textTheme.h4.copyWith(
            color: shadTheme.colorScheme.primaryForeground,
          ),
        ),
        backgroundColor: shadTheme.colorScheme.primary,
        iconTheme: IconThemeData(
          color: shadTheme.colorScheme.primaryForeground,
        ),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Author and Date
            Row(
              children: [
                if (widget.contentItem.publishedAt != null)
                  Text(
                    'Published: ${dateFormat.format(widget.contentItem.publishedAt!)}',
                    style: shadTheme.textTheme.small.copyWith(
                      color: shadTheme.colorScheme.mutedForeground,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Thumbnail Image
            if (hasImage)
              Center(
                child: Hero(
                  tag: 'content_thumbnail_${widget.contentItem.id}',
                  child: ClipRRect(
                    borderRadius: shadTheme.radius,
                    child: Image.network(
                      imageUrl!,
                      height: 250,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      loadingBuilder:
                          (context, child, progress) =>
                              progress == null
                                  ? child
                                  : const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                      errorBuilder:
                          (context, error, stackTrace) => Container(
                            height: 250,
                            color: shadTheme.colorScheme.muted,
                            alignment: Alignment.center,
                            child: Icon(
                              LucideIcons.imageOff,
                              size: 60,
                              color: shadTheme.colorScheme.mutedForeground,
                            ),
                          ),
                    ),
                  ),
                ),
              ),
            if (hasImage) const SizedBox(height: 20),

            // Tags
            if (widget.contentItem.tags.isNotEmpty)
              Wrap(
                spacing: 8.0,
                runSpacing: 4.0,
                children:
                    widget.contentItem.tags
                        .map(
                          (tag) => ShadBadge.outline(
                            child: Text(
                              tag,
                              style: shadTheme.textTheme.small.copyWith(
                                color: shadTheme.colorScheme.secondary,
                              ),
                            ),
                            backgroundColor: shadTheme.colorScheme.secondary
                                .withOpacity(0.1),
                          ),
                        )
                        .toList(),
              ),
            if (widget.contentItem.tags.isNotEmpty) const SizedBox(height: 20),

            // Content-Specific Display
            _buildContentSpecificSection(
              context,
              widget.contentItem,
              shadTheme,
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildContentSpecificSection(
    BuildContext context,
    ContentItemModel item,
    ShadThemeData theme,
  ) {
    const textBasedTypes = {
      'blog',
      'newsletter',
      'resource',
      'case_study',
      'article',
      'educational_module',
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display Body Content using Html widget or Text
        if (textBasedTypes.contains(item.type))
          Html(
            data:
                item.bodyContent ??
                item.summary ??
                '<p>No content available.</p>',
            style: {
              "body": Style(
                fontSize: FontSize(theme.textTheme.p.fontSize ?? 16),
                color: theme.colorScheme.foreground,
                lineHeight: LineHeight.number(1.6),
              ),
              "h1": Style(
                fontSize: FontSize(theme.textTheme.h1.fontSize ?? 32),
                fontWeight: FontWeight.bold,
              ),
              "h2": Style(
                fontSize: FontSize(theme.textTheme.h2.fontSize ?? 28),
                fontWeight: FontWeight.bold,
              ),
              "h3": Style(
                fontSize: FontSize(theme.textTheme.h3.fontSize ?? 24),
                fontWeight: FontWeight.bold,
              ),
              "h4": Style(
                fontSize: FontSize(theme.textTheme.h4.fontSize ?? 20),
                fontWeight: FontWeight.bold,
              ),
              "p": Style(margin: Margins.only(bottom: 16)),
              "a": Style(
                color: theme.colorScheme.primary,
                textDecoration: TextDecoration.none,
              ),
            },
            onLinkTap: (url, _, __) {
              print('Tapped link: $url');
              // TODO: Implement URL launching if needed
            },
          ),

        // Display Podcast specific info
        if (item.type == 'podcast') ...[
          if (item.summary != null && item.summary!.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.muted.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: theme.colorScheme.border, width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        LucideIcons.fileText,
                        size: 18,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Summary',
                        style: theme.textTheme.large.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  SelectableText(
                    item.summary!,
                    style: theme.textTheme.p.copyWith(
                      height: 1.6,
                      fontSize: 15,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
          _buildPodcastPlayer(item, theme),
          // Display Show Notes / Transcript (if available)
          if (item.bodyContent != null && item.bodyContent!.isNotEmpty) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.card,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: theme.colorScheme.border, width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        LucideIcons.fileText,
                        size: 18,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Show Notes / Transcript',
                        style: theme.textTheme.large.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Html(
                    data: item.bodyContent!,
                    style: {
                      "body": Style(
                        fontSize: FontSize(theme.textTheme.p.fontSize ?? 16),
                        color: theme.colorScheme.foreground,
                        lineHeight: LineHeight.number(1.6),
                      ),
                      "p": Style(margin: Margins.only(bottom: 16)),
                    },
                  ),
                ],
              ),
            ),
          ],
        ],

        // Display Webinar specific info
        if (item.type == 'webinar') ...[
          if (item.summary != null && item.summary!.isNotEmpty) ...[
            Text('Webinar Details:', style: theme.textTheme.h4),
            const SizedBox(height: 8),
            SelectableText(item.summary!, style: theme.textTheme.p),
            const SizedBox(height: 16),
          ],
          if (item.mediaUrl != null && item.mediaUrl!.isNotEmpty) ...[
            // Webinars might still use media_url for external links
            ShadButton(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(LucideIcons.link, size: 16),
                  const SizedBox(width: 8),
                  const Text('Access Webinar'),
                ],
              ),
              onPressed: () {
                ShadToaster.of(context).show(
                  ShadToast(
                    description: Text('Accessing ${item.title} (Placeholder)'),
                  ),
                );
                // TODO: Implement URL launching if needed (requires url_launcher package)
                // _launchUrl(context, item.mediaUrl);
              },
            ),
          ],
        ],
      ],
    );
  }

  /// Builds podcast player widget
  Widget _buildPodcastPlayer(ContentItemModel item, ShadThemeData theme) {
    final audioUrl = _getPodcastAudioUrl();

    if (audioUrl == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.muted.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.colorScheme.border, width: 1),
        ),
        child: Row(
          children: [
            Icon(Icons.warning, size: 20, color: theme.colorScheme.destructive),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Audio file not available for this podcast.',
                style: theme.textTheme.p.copyWith(
                  color: theme.colorScheme.destructive,
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Create podcast model for the enhanced player
    final podcastModel = CoFunderPodcastModel(
      id: item.id,
      collectionId: item.collectionId,
      collectionName: item.collectionName,
      created: item.created,
      updated: item.updated,
      contentItemSlug: item.slug,
      episodeNumber: 1,
      playCount: 0,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.border, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                LucideIcons.headphones,
                size: 18,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Podcast Episode',
                style: theme.textTheme.large.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          EnhancedPodcastPlayerWidget(
            podcast: podcastModel,
            audioUrl: audioUrl,
          ),
        ],
      ),
    );
  }
}
