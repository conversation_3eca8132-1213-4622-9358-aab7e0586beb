import 'dart:io';
import 'package:pocketbase/pocketbase.dart';

void main() async {
  // Initialize PocketBase client
  final pb = PocketBase('http://127.0.0.1:8090');

  try {
    print('Creating sample content items...');

    // Sample blog content
    final blogContent = {
      'title': 'Understanding Litigation Funding',
      'slug': 'understanding-litigation-funding',
      'type': 'blog',
      'body_content': '''
        <h2>What is Litigation Funding?</h2>
        <p>Litigation funding, also known as legal financing or third-party funding, is a financial arrangement where a third party provides capital to fund legal proceedings in exchange for a portion of any settlement or judgment.</p>
        
        <h3>Key Benefits</h3>
        <ul>
          <li>Access to justice for those who cannot afford legal costs</li>
          <li>Risk mitigation for claimants</li>
          <li>Professional case assessment</li>
          <li>No upfront costs</li>
        </ul>
        
        <h3>How It Works</h3>
        <p>The process typically involves:</p>
        <ol>
          <li>Case evaluation by funding experts</li>
          <li>Due diligence and risk assessment</li>
          <li>Funding agreement negotiation</li>
          <li>Ongoing case monitoring</li>
        </ol>
      ''',
      'excerpt': 'Learn about litigation funding and how it can provide access to justice for claimants who cannot afford legal costs.',
      'is_published': true,
      'published_date': DateTime.now().toIso8601String(),
      'author': '3Pay Global Team',
      'tags': 'litigation funding, legal finance, access to justice',
      'meta_description': 'Understanding litigation funding: benefits, process, and how it provides access to justice.',
      'access_level': 'public'
    };

    // Sample podcast content
    final podcastContent = {
      'title': 'The Future of Legal Technology',
      'slug': 'future-of-legal-technology',
      'type': 'podcast',
      'body_content': '''
        <h2>Episode Summary</h2>
        <p>In this episode, we explore the rapidly evolving landscape of legal technology and its impact on litigation funding.</p>
        
        <h3>Topics Covered</h3>
        <ul>
          <li>AI in legal case assessment</li>
          <li>Blockchain for transparent funding</li>
          <li>Digital platforms for case management</li>
          <li>Future trends in legal tech</li>
        </ul>
      ''',
      'excerpt': 'Exploring the intersection of technology and legal services in the modern litigation funding landscape.',
      'is_published': true,
      'published_date': DateTime.now().subtract(Duration(days: 7)).toIso8601String(),
      'author': 'Legal Tech Experts',
      'tags': 'legal technology, AI, blockchain, innovation',
      'meta_description': 'Discover how technology is transforming litigation funding and legal services.',
      'access_level': 'public',
      'episode_number': 1,
      'duration_seconds': 1800, // 30 minutes
      'media_url': 'https://example.com/podcast/episode1.mp3'
    };

    // Sample educational module
    final educationalContent = {
      'title': 'Group Litigation Fundamentals',
      'slug': 'group-litigation-fundamentals',
      'type': 'educational_module',
      'body_content': '''
        <h2>Learning Objectives</h2>
        <p>By the end of this module, you will understand:</p>
        <ul>
          <li>The basics of group litigation</li>
          <li>When group litigation is appropriate</li>
          <li>The role of funding in group actions</li>
          <li>Key considerations for claimants</li>
        </ul>
        
        <h3>Module 1: Introduction to Group Litigation</h3>
        <p>Group litigation allows multiple claimants with similar claims to join together in a single legal action...</p>
        
        <h3>Module 2: Funding Mechanisms</h3>
        <p>Understanding how litigation funding works in group actions...</p>
        
        <h3>Module 3: Case Studies</h3>
        <p>Real-world examples of successful group litigation cases...</p>
      ''',
      'excerpt': 'A comprehensive educational module covering the fundamentals of group litigation and funding.',
      'is_published': true,
      'published_date': DateTime.now().subtract(Duration(days: 14)).toIso8601String(),
      'author': 'Legal Education Team',
      'tags': 'group litigation, education, legal training',
      'meta_description': 'Learn the fundamentals of group litigation and how funding works in collective legal actions.',
      'access_level': 'level_1'
    };

    // Try to create the collection first
    try {
      await pb.collections.create(body: {
        'name': 'content_items',
        'type': 'base',
        'schema': [
          {
            'name': 'title',
            'type': 'text',
            'required': true,
          },
          {
            'name': 'slug',
            'type': 'text',
            'required': true,
          },
          {
            'name': 'type',
            'type': 'select',
            'required': true,
            'options': {
              'values': ['blog', 'podcast', 'educational_module']
            }
          },
          {
            'name': 'body_content',
            'type': 'editor',
            'required': false,
          },
          {
            'name': 'excerpt',
            'type': 'text',
            'required': false,
          },
          {
            'name': 'is_published',
            'type': 'bool',
            'required': false,
          },
          {
            'name': 'published_date',
            'type': 'date',
            'required': false,
          },
          {
            'name': 'author',
            'type': 'text',
            'required': false,
          },
          {
            'name': 'tags',
            'type': 'text',
            'required': false,
          },
          {
            'name': 'meta_description',
            'type': 'text',
            'required': false,
          },
          {
            'name': 'access_level',
            'type': 'select',
            'required': false,
            'options': {
              'values': ['public', 'level_1', 'level_2', 'level_3', 'level_4']
            }
          },
          {
            'name': 'episode_number',
            'type': 'number',
            'required': false,
          },
          {
            'name': 'duration_seconds',
            'type': 'number',
            'required': false,
          },
          {
            'name': 'media_url',
            'type': 'url',
            'required': false,
          },
          {
            'name': 'play_count',
            'type': 'number',
            'required': false,
          },
        ]
      });
      print('✓ Created content_items collection');
    } catch (e) {
      print('Collection might already exist or creation failed: $e');
    }

    // Create sample content items
    final contents = [blogContent, podcastContent, educationalContent];
    
    for (final content in contents) {
      try {
        await pb.collection('content_items').create(body: content);
        print('✓ Created content: ${content['title']}');
      } catch (e) {
        print('✗ Failed to create content: ${content['title']} - $e');
      }
    }

    print('\nSample content creation completed!');
    
  } catch (e) {
    print('Error: $e');
    exit(1);
  }
}
