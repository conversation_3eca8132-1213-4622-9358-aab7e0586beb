import 'dart:io';
import 'dart:math';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Script to create sample content data for the content_items collection
/// This will populate blog posts and podcasts for testing the landing page
Future<void> main() async {
  LoggerService.info('🚀 Starting sample content data creation...');

  try {
    // Initialize PocketBase service
    final pocketBaseService = PocketBaseService();
    await pocketBaseService.init();

    LoggerService.info('📊 Creating sample content data...');

    // Create sample blog posts
    await createSampleBlogPosts(pocketBaseService);
    
    // Create sample podcasts
    await createSamplePodcasts(pocketBaseService);

    LoggerService.info('✅ Sample content data creation completed successfully!');
  } catch (e) {
    LoggerService.error('❌ Error creating sample content data', e);
    exit(1);
  }
}

/// Create sample blog posts
Future<void> createSampleBlogPosts(PocketBaseService pbService) async {
  LoggerService.info('📝 Creating sample blog posts...');

  final blogPosts = [
    {
      'type': 'blog',
      'title': 'Understanding Litigation Funding: A Comprehensive Guide',
      'slug': 'understanding-litigation-funding-guide',
      'summary': 'Learn the fundamentals of litigation funding and how it can help you access justice without financial barriers.',
      'body_content': '''
        <h2>What is Litigation Funding?</h2>
        <p>Litigation funding, also known as legal financing or third-party funding, is a financial arrangement where a third party provides capital to fund legal proceedings in exchange for a portion of any settlement or judgment.</p>
        
        <h3>Key Benefits</h3>
        <ul>
          <li>Access to justice without upfront legal costs</li>
          <li>Risk mitigation for claimants</li>
          <li>Professional case assessment</li>
          <li>Ongoing case management support</li>
        </ul>
        
        <h3>How It Works</h3>
        <p>The process typically involves:</p>
        <ol>
          <li>Initial case assessment</li>
          <li>Due diligence review</li>
          <li>Funding agreement negotiation</li>
          <li>Ongoing case monitoring</li>
        </ol>
        
        <p>At 3Pay Global, we specialize in connecting solicitors with co-funders to ensure your case gets the financial backing it deserves.</p>
      ''',
      'status': 'published',
      'target_user_levels': [1, 2, 3, 4],
      'tags': ['litigation', 'funding', 'legal', 'guide'],
      'analytics_views': Random().nextInt(500) + 100,
      'published_at': DateTime.now().subtract(Duration(days: Random().nextInt(30))).toIso8601String(),
    },
    {
      'type': 'blog',
      'title': 'The Role of Co-Funders in Modern Legal Practice',
      'slug': 'role-of-co-funders-modern-legal-practice',
      'summary': 'Explore how co-funders are revolutionizing access to legal services and supporting complex litigation cases.',
      'body_content': '''
        <h2>Co-Funders: The New Pillars of Legal Support</h2>
        <p>Co-funders play a crucial role in the modern legal landscape by providing the financial resources necessary for complex litigation cases.</p>
        
        <h3>Types of Co-Funders</h3>
        <ul>
          <li><strong>Institutional Funders:</strong> Large organizations with significant capital</li>
          <li><strong>Private Investors:</strong> Individual investors seeking legal investment opportunities</li>
          <li><strong>Specialized Funds:</strong> Funds dedicated specifically to litigation financing</li>
        </ul>
        
        <h3>Investment Criteria</h3>
        <p>Co-funders typically evaluate cases based on:</p>
        <ul>
          <li>Strength of the legal claim</li>
          <li>Potential damages or settlement value</li>
          <li>Quality of legal representation</li>
          <li>Timeline to resolution</li>
        </ul>
        
        <p>Understanding these criteria helps solicitors present their cases more effectively to potential co-funders.</p>
      ''',
      'status': 'published',
      'target_user_levels': [2, 3, 4],
      'tags': ['co-funders', 'investment', 'legal-practice', 'solicitors'],
      'analytics_views': Random().nextInt(400) + 80,
      'published_at': DateTime.now().subtract(Duration(days: Random().nextInt(25))).toIso8601String(),
    },
    {
      'type': 'blog',
      'title': 'Group Litigation: Strength in Numbers',
      'slug': 'group-litigation-strength-in-numbers',
      'summary': 'Discover the advantages of group litigation and how collective action can lead to better outcomes for claimants.',
      'body_content': '''
        <h2>The Power of Collective Legal Action</h2>
        <p>Group litigation allows multiple claimants with similar legal issues to join forces, creating a stronger case and sharing costs.</p>
        
        <h3>Advantages of Group Litigation</h3>
        <ul>
          <li>Shared legal costs among participants</li>
          <li>Stronger negotiating position</li>
          <li>Economies of scale in case preparation</li>
          <li>Increased media attention and public awareness</li>
        </ul>
        
        <h3>Types of Group Actions</h3>
        <p>There are several forms of group litigation:</p>
        <ul>
          <li><strong>Class Actions:</strong> One or more representatives act for the entire group</li>
          <li><strong>Group Litigation Orders (GLOs):</strong> UK-specific mechanism for managing multiple related claims</li>
          <li><strong>Test Cases:</strong> Representative cases that establish precedent for similar claims</li>
        </ul>
        
        <h3>Getting Started</h3>
        <p>If you believe you have a case suitable for group litigation, contact our team to discuss your options and connect with other potential claimants.</p>
      ''',
      'status': 'published',
      'target_user_levels': [1, 2, 3, 4],
      'tags': ['group-litigation', 'class-action', 'collective', 'claimants'],
      'analytics_views': Random().nextInt(600) + 150,
      'published_at': DateTime.now().subtract(Duration(days: Random().nextInt(20))).toIso8601String(),
    },
  ];

  for (int i = 0; i < blogPosts.length; i++) {
    try {
      final record = await pbService.createRecord(
        collectionName: 'content_items',
        data: blogPosts[i],
      );
      LoggerService.info('   ✅ Created blog post: ${blogPosts[i]['title']} (${record.id})');
    } catch (e) {
      LoggerService.error('   ❌ Failed to create blog post: ${blogPosts[i]['title']}', e);
    }
  }

  LoggerService.info('✅ Sample blog posts created');
}

/// Create sample podcasts
Future<void> createSamplePodcasts(PocketBaseService pbService) async {
  LoggerService.info('🎧 Creating sample podcasts...');

  final podcasts = [
    {
      'type': 'podcast',
      'title': 'Legal Tech Revolution: How Technology is Transforming Law',
      'slug': 'legal-tech-revolution-transforming-law',
      'summary': 'Join us as we explore the latest technological innovations in the legal industry and their impact on litigation funding.',
      'body_content': '''
        <h2>Episode Overview</h2>
        <p>In this episode, we dive deep into the technological revolution that's reshaping the legal industry.</p>
        
        <h3>Topics Covered</h3>
        <ul>
          <li>AI-powered case analysis</li>
          <li>Blockchain in legal contracts</li>
          <li>Digital evidence management</li>
          <li>Online dispute resolution platforms</li>
        </ul>
        
        <h3>Key Takeaways</h3>
        <p>Technology is not replacing lawyers but empowering them to work more efficiently and provide better outcomes for clients.</p>
      ''',
      'media_url': 'https://example.com/podcast/legal-tech-revolution.mp3',
      'status': 'published',
      'target_user_levels': [2, 3, 4],
      'tags': ['legal-tech', 'innovation', 'AI', 'blockchain'],
      'analytics_views': Random().nextInt(300) + 50,
      'published_at': DateTime.now().subtract(Duration(days: Random().nextInt(15))).toIso8601String(),
    },
    {
      'type': 'podcast',
      'title': 'Successful Case Study: David vs. Goliath in Corporate Litigation',
      'slug': 'successful-case-study-david-vs-goliath',
      'summary': 'A detailed analysis of how strategic litigation funding helped a small business take on a multinational corporation and win.',
      'body_content': '''
        <h2>Case Background</h2>
        <p>This episode examines a landmark case where strategic litigation funding enabled a small business to successfully challenge a multinational corporation.</p>
        
        <h3>Key Success Factors</h3>
        <ul>
          <li>Strong legal merit assessment</li>
          <li>Experienced legal team</li>
          <li>Adequate funding for full case duration</li>
          <li>Strategic case management</li>
        </ul>
        
        <h3>Lessons Learned</h3>
        <p>This case demonstrates the importance of thorough preparation and the right funding partnership in achieving justice.</p>
      ''',
      'media_url': 'https://example.com/podcast/david-vs-goliath-case-study.mp3',
      'status': 'published',
      'target_user_levels': [1, 2, 3, 4],
      'tags': ['case-study', 'corporate-litigation', 'success-story', 'funding'],
      'analytics_views': Random().nextInt(450) + 120,
      'published_at': DateTime.now().subtract(Duration(days: Random().nextInt(10))).toIso8601String(),
    },
  ];

  for (int i = 0; i < podcasts.length; i++) {
    try {
      final record = await pbService.createRecord(
        collectionName: 'content_items',
        data: podcasts[i],
      );
      LoggerService.info('   ✅ Created podcast: ${podcasts[i]['title']} (${record.id})');
    } catch (e) {
      LoggerService.error('   ❌ Failed to create podcast: ${podcasts[i]['title']}', e);
    }
  }

  LoggerService.info('✅ Sample podcasts created');
}
